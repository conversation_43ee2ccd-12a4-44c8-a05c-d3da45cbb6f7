import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: '#00aff0',
          50: '#e6f7ff',
          100: '#b3e8ff',
          200: '#80d9ff',
          300: '#4dcaff',
          400: '#1abbff',
          500: '#00aff0',
          600: '#0099d6',
          700: '#0083bd',
          800: '#006da3',
          900: '#005789'
        },
        accent: {
          DEFAULT: '#00aff0',
          500: '#00aff0',
          600: '#0099d6',
          700: '#0083bd'
        },
        neon: {
          blue: '#00aff0',
          cyan: '#00d4ff',
          light: '#4dcaff'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'gradient': 'gradient 6s ease infinite',
        'glow': 'glow 3s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-ring': 'pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        gradient: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
      },
      boxShadow: {
        'neon': '0 0 20px rgba(0, 175, 240, 0.5)',
        'neon-lg': '0 0 40px rgba(0, 175, 240, 0.7)',
      },
    },
  },
  plugins: [],
};

export default config; 