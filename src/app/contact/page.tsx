"use client";

import { Mail, MessageCircle, Send, Clock, MapPin } from 'lucide-react';
import { Navigation } from '@/components/sections/navigation';
import { Footer } from '@/components/sections/footer';
import { Interactive3DCard, Animated3DElements } from '@/components/ui/animated-3d-elements';
import { Morphing3DBlob, Particle3DSystem } from '@/components/ui/floating-3d-icons';

export default function ContactPage() {
  return (
    <>
      <Navigation />
      <main className="min-h-screen bg-white pt-20 relative overflow-hidden">
        {/* 3D Background Elements */}
        <Animated3DElements variant="minimal" />
        <Particle3DSystem particleCount={6} />
        <Morphing3DBlob 
          className="absolute top-32 right-[10%]" 
          size="md" 
          color="primary" 
        />
        <Morphing3DBlob 
          className="absolute bottom-32 left-[8%]" 
          size="sm" 
          color="secondary" 
        />
        
        <div className="max-w-6xl mx-auto px-6 lg:px-8 py-16 relative">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-sm font-medium text-primary mb-8">
              <MessageCircle className="w-4 h-4" />
              Contact Us
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 md:mb-6 tracking-tight px-4">
              Get in Touch with <span className="gradient-text">Us</span>
            </h1>

            <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
              Have questions or need help with something? Don&apos;t hesitate to reach out to us!
            </p>
          </div>

          {/* Contact Methods */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 max-w-4xl mx-auto mb-12 md:mb-16 px-4">
            {/* Email Card */}
            <Interactive3DCard
              className="bg-white p-6 md:p-8 rounded-xl md:rounded-2xl border border-gray-200 shadow-sm text-center"
              intensity={0.6}
            >
              <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-primary" />
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-4">E-mail</h3>
              
              <a 
                href="mailto:<EMAIL>"
                className="text-lg text-primary hover:text-primary/80 transition-colors font-medium"
              >
                <EMAIL>
              </a>
              
              <p className="text-gray-600 text-sm mt-4">
                Send us an email and we&apos;ll get back to you within 24 hours
              </p>
            </Interactive3DCard>

            {/* Telegram Card */}
            <Interactive3DCard
              className="bg-white p-6 md:p-8 rounded-xl md:rounded-2xl border border-gray-200 shadow-sm text-center"
              intensity={0.6}
            >
              <div className="w-16 h-16 bg-blue-500/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Send className="w-8 h-8 text-blue-500" />
              </div>
              
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Telegram</h3>
              
              <a 
                href="https://t.me/OFautofollower"
                target="_blank"
                rel="noopener noreferrer"
                className="text-lg text-blue-500 hover:text-blue-600 transition-colors font-medium"
              >
                @OFautofollower
              </a>
              
              <p className="text-gray-600 text-sm mt-4">
                Join our Telegram for instant support and updates
              </p>
            </Interactive3DCard>
          </div>

          {/* Additional Info */}
          <div className="bg-gradient-to-r from-primary/5 to-blue-500/5 p-6 md:p-8 rounded-xl md:rounded-2xl border border-primary/10 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 text-center">
              <div className="flex items-center justify-center gap-3">
                <Clock className="w-6 h-6 text-primary" />
                <div>
                  <h4 className="font-semibold text-gray-900">Response Time</h4>
                  <p className="text-gray-600 text-sm">Usually within 24 hours</p>
                </div>
              </div>
              
              <div className="flex items-center justify-center gap-3">
                <MapPin className="w-6 h-6 text-primary" />
                <div>
                  <h4 className="font-semibold text-gray-900">Support Hours</h4>
                  <p className="text-gray-600 text-sm">24/7 via Telegram & Email</p>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-20">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 text-center mb-8 md:mb-12 px-4">
              Frequently Asked Questions
            </h2>

            <div className="grid gap-4 md:gap-6 max-w-4xl mx-auto px-4">
              {[
                {
                  q: "How quickly do you respond to support requests?",
                  a: "We typically respond to all inquiries within 24 hours. For urgent matters, Telegram usually provides faster response times."
                },
                {
                  q: "What's the best way to contact you?",
                  a: "For general inquiries, email works great. For immediate support or quick questions, Telegram is your best bet."
                },
                {
                  q: "Do you provide technical support?",
                  a: "Yes! We provide full technical support for all OFAutoFollower features via both email and Telegram."
                },
                {
                  q: "Can I schedule a call or demo?",
                  a: "Absolutely! Reach out via email or Telegram and we'll be happy to arrange a personalized demo or consultation."
                }
              ].map((faq, index) => (
                <Interactive3DCard
                  key={index}
                  className="bg-white p-6 rounded-xl border border-gray-200"
                  intensity={0.3}
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.q}</h3>
                  <p className="text-gray-600">{faq.a}</p>
                </Interactive3DCard>
              ))}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
