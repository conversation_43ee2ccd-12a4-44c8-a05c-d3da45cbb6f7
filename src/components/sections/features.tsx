"use client";

import {
  Users,
  RefreshCw,
  TrendingUp,
  MessageCircle,
  DollarSign,
  Shield,
  Zap,
  Target,
  ArrowRight
} from 'lucide-react';
import { Animated3DElements, Interactive3DCard } from '@/components/ui/animated-3d-elements';
import { FloatingIconsSet, Particle3DSystem } from '@/components/ui/floating-3d-icons';

const features = [
  {
    icon: Users,
    title: "Auto-Follow New OF Users",
    description: "Automatically connect with fresh signups on OnlyFans. Be among the first creators they discover.",
    stats: "10K+ new users daily",
    bgColor: "#00aff0",
    glowColor: "rgba(0, 175, 240, 0.2)"
  },
  {
    icon: RefreshCw,
    title: "Recapture Expired Fans",
    description: "Reconnect with high-value fans who've expired. Keep the ability to message and sell content.",
    stats: "85% recovery rate",
    bgColor: "#0099d6",
    glowColor: "rgba(0, 153, 214, 0.2)"
  },
  {
    icon: TrendingUp,
    title: "Cross-Account Promotion",
    description: "Leverage multiple accounts to maximize exposure. Auto-follow fans between your profiles.",
    stats: "3x engagement boost",
    bgColor: "#0083bd",
    glowColor: "rgba(0, 131, 189, 0.2)"
  },
  {
    icon: MessageCircle,
    title: "Priority Mass Messaging",
    description: "Send personalized messages that appear first in fans' inboxes. Higher open rates guaranteed.",
    stats: "90% open rate",
    bgColor: "#006da3",
    glowColor: "rgba(0, 109, 163, 0.2)"
  },
  {
    icon: DollarSign,
    title: "Smart Revenue Recovery",
    description: "Convert expired fans into paying customers with targeted PPV content and offers.",
    stats: "$2.5M+ recovered",
    bgColor: "#1abbff",
    glowColor: "rgba(26, 187, 255, 0.2)"
  },
  {
    icon: Shield,
    title: "Safe & Compliant",
    description: "Human-like behavior patterns keep your account secure while maximizing growth.",
    stats: "100% safe",
    bgColor: "#00aff0",
    glowColor: "rgba(0, 175, 240, 0.2)"
  }
];

export function Features() {
  return (
    <section id="features" className="py-24 relative overflow-hidden">
      {/* 3D Background Elements */}
      <Animated3DElements variant="section" />
      <FloatingIconsSet variant="features" />
      <Particle3DSystem particleCount={8} />

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full bg-primary/10 border border-primary/20 text-sm font-medium text-primary mb-6">
            <Zap className="w-4 h-4" />
            Powerful Features
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Everything you need to
            <br />
            <span className="gradient-text">dominate OnlyFans</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Advanced automation tools designed to multiply your reach and revenue on OnlyFans
          </p>
        </div>
        
        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className="group relative"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <Interactive3DCard
                  className="relative bg-white border border-gray-200 rounded-2xl p-8 hover:border-primary/40 hover:shadow-lg transition-all duration-300 h-full"
                  intensity={0.6}
                >
                  {/* Icon with solid background */}
                  <div className="relative mb-6">
                    <div
                      className="absolute inset-0 blur-2xl opacity-20 group-hover:opacity-30 transition-opacity"
                      style={{ backgroundColor: feature.glowColor }}
                    ></div>
                    <div
                      className="relative w-14 h-14 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300"
                      style={{ backgroundColor: feature.bgColor }}
                    >
                      <Icon className="w-7 h-7 text-white" />
                    </div>
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {feature.description}
                  </p>
                  
                  {/* Stats */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-semibold text-primary bg-primary/10 px-3 py-1 rounded-full">
                      {feature.stats}
                    </span>
                    <ArrowRight className="w-5 h-5 text-primary opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-2 transition-all duration-300" />
                  </div>
                </Interactive3DCard>
              </div>
            );
          })}
        </div>

        {/* Interactive Demo Section */}
        <div className="relative rounded-3xl overflow-hidden bg-gray-50 border border-gray-200 p-12 text-center" data-aos="fade-up">

          <div className="relative">
            <Target className="w-16 h-16 text-primary mx-auto mb-6" />
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to 10x your OnlyFans growth?
            </h3>
            <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
              Join thousands of creators who are automating their success with ofautofollower
            </p>
            <a 
              href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf" 
              target="_blank"
              className="inline-flex items-center gap-3 premium-button text-white font-semibold px-8 py-4 rounded-full shadow-neon hover-lift"
            >
              <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="24" width="24">
                <defs>
                  <linearGradient id="featChromeIconA" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#d93025"/>
                    <stop offset="1" stopColor="#ea4335"/>
                  </linearGradient>
                  <linearGradient id="featChromeIconB" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#fcc934"/>
                    <stop offset="1" stopColor="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="featChromeIconC" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#1e8e3e"/>
                    <stop offset="1" stopColor="#34a853"/>
                  </linearGradient>
                </defs>
                <circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/>
                <path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/>
                <path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#featChromeIconA)'}}/>
                <circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/>
                <path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#featChromeIconB)'}}/>
                <path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#featChromeIconC)'}}/>
              </svg>
              Start Growing Today
              <ArrowRight className="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}