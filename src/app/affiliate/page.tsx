"use client";

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, DollarSign, TrendingUp, <PERSON>R<PERSON>, Co<PERSON>, Check } from 'lucide-react';
import { Navigation } from '@/components/sections/navigation';
import { Footer } from '@/components/sections/footer';
import { Animated3DElements, Interactive3DCard } from '@/components/ui/animated-3d-elements';
import { Morphing3DBlob, Particle3DSystem } from '@/components/ui/floating-3d-icons';

export default function AffiliatePage() {
  const [copied, setCopied] = useState(false);

  const handleCopyCode = () => {
    if (typeof window !== 'undefined' && navigator.clipboard) {
      navigator.clipboard.writeText('AFFILIATE40');
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <>
      <Navigation />
      <main className="min-h-screen bg-white pt-20 relative overflow-hidden">
        {/* 3D Background Elements */}
        <Animated3DElements variant="minimal" />
        <Particle3DSystem particleCount={6} />
        <Morphing3DBlob
          className="absolute top-32 right-[10%]"
          size="md"
          color="primary"
        />
        <Morphing3DBlob
          className="absolute bottom-32 left-[8%]"
          size="sm"
          color="secondary"
        />

        <div className="max-w-4xl mx-auto px-6 lg:px-8 relative">
          {/* Compact Hero */}
          <div className="text-center py-16">
            <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-primary/10 border border-primary/20 text-xs font-medium text-primary mb-6">
              <DollarSign className="w-3.5 h-3.5" />
              Affiliate Program
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 tracking-tight px-4">
              Start Earning with <span className="gradient-text">OFAutoFollower</span>
            </h1>
            <p className="text-base sm:text-lg text-gray-600 mb-4 max-w-3xl mx-auto px-4">
              Spread the word about OFAutoFollower and earn a <strong>40% commission, for life.</strong>
            </p>
            <p className="text-lg sm:text-xl font-semibold text-gray-900 mb-6 md:mb-8 px-4">
              Earn $40 per month for every referral. FOREVER!
            </p>

            {/* Key Message */}
            <div className="bg-gradient-to-r from-primary/10 to-blue-500/10 p-6 rounded-2xl mb-8 max-w-3xl mx-auto border border-primary/20">
              <p className="text-lg font-medium text-gray-900 mb-2">
                You'll get $40 every month for every license bought with your code — forever.
              </p>
              <p className="text-base text-gray-700">
                Best of all - There are no limits on how much you can earn.
              </p>
            </div>

            {/* Compact Benefits Grid */}
            <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-8 mb-6 md:mb-8 px-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-white" />
                </div>
                <div className="text-left">
                  <p className="text-base font-medium text-gray-900">$40/month</p>
                  <p className="text-sm text-gray-600">Per referral, forever</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div className="text-left">
                  <p className="text-base font-medium text-gray-900">No limits</p>
                  <p className="text-sm text-gray-600">Unlimited earnings</p>
                </div>
              </div>
            </div>

            {/* Benefits Section */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Benefits</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-4xl mx-auto px-4">
                <Interactive3DCard
                  className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm"
                  intensity={0.5}
                >
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0 mt-1" />
                    <div>
                      <p className="font-semibold text-gray-900 mb-1">You get:</p>
                      <p className="text-gray-700">$40 per month for every referral, forever</p>
                    </div>
                  </div>
                </Interactive3DCard>

                <Interactive3DCard
                  className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm"
                  intensity={0.5}
                >
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0 mt-1" />
                    <div>
                      <p className="font-semibold text-gray-900 mb-1">Your referrals get:</p>
                      <p className="text-gray-700">50% OFF subscription forever</p>
                    </div>
                  </div>
                </Interactive3DCard>
              </div>
            </div>

            {/* How It Works */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">How It Works</h2>
              <div className="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">1</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Get Your Code</h3>
                  <p className="text-gray-600 text-sm">Sign up and receive your unique referral code</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">2</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Share & Promote</h3>
                  <p className="text-gray-600 text-sm">Share your code with creators who need OFAutoFollower</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">3</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Earn Forever</h3>
                  <p className="text-gray-600 text-sm">Get $40/month for every active subscription</p>
                </div>
              </div>
            </div>

            {/* Sample Code */}
            <div className="bg-white p-6 rounded-xl mb-8 border border-gray-200 shadow-sm">
              <p className="text-gray-600 text-sm mb-3">Your referral code will look like:</p>
              <div className="flex items-center justify-center gap-2">
                <code className="bg-gray-100 px-4 py-3 rounded-lg text-gray-900 font-mono text-lg">
                  AFFILIATE40
                </code>
                <button
                  onClick={handleCopyCode}
                  className="p-3 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Copy code"
                >
                  {copied ? (
                    <Check className="w-5 h-5 text-green-500" />
                  ) : (
                    <Copy className="w-5 h-5 text-gray-600" />
                  )}
                </button>
              </div>
            </div>

            {/* Earnings Calculator */}
            <div className="bg-gradient-to-r from-primary/5 to-blue-500/5 p-8 rounded-2xl mb-8 border border-primary/10">
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">Potential Monthly Earnings</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-white p-4 rounded-lg">
                  <div className="text-2xl font-bold text-primary">$200</div>
                  <div className="text-sm text-gray-600">5 referrals</div>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <div className="text-2xl font-bold text-primary">$800</div>
                  <div className="text-sm text-gray-600">20 referrals</div>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <div className="text-2xl font-bold text-primary">$2,000</div>
                  <div className="text-sm text-gray-600">50 referrals</div>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <div className="text-2xl font-bold text-primary">$4,000</div>
                  <div className="text-sm text-gray-600">100 referrals</div>
                </div>
              </div>
              <p className="text-center text-gray-600 text-sm mt-4">
                * These earnings continue every month, forever
              </p>
            </div>

                        {/* Get Started Button */}
            <div className="space-y-6">
              <a
                href="https://onlyfans-autofollower.getrewardful.com/signup"
                target="_blank"
                rel="noopener noreferrer"
                className="premium-button text-white font-bold px-10 py-5 rounded-xl inline-flex items-center gap-3 shadow-lg justify-center text-xl hover-lift"
              >
                Join Affiliate Program
                <ArrowRight className="w-6 h-6" />
              </a>

              <div className="text-center space-y-2">
                <p className="text-gray-600 text-sm">
                  Questions? Contact us at{' '}
                  <a href="mailto:<EMAIL>" className="text-primary hover:text-primary/80 transition-colors font-medium">
                    <EMAIL>
                  </a>
                </p>
                <p className="text-gray-500 text-xs">
                  Start earning today - no upfront costs, no hidden fees
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}