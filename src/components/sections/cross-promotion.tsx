"use client";

import { Network, Shuffle, TrendingUp, ArrowR<PERSON>, Users } from 'lucide-react';

export function CrossPromotion() {
  return (
    <section className="py-24 section-bg">

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Right Visual - Swapped to left */}
          <div className="relative order-2 lg:order-1" data-aos="fade-up" data-aos-delay="200">
            <div className="card-bg p-8 rounded-2xl backdrop-blur-xl">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Account Network</h3>
                  <div className="flex items-center gap-2 text-sm text-primary">
                    <Network className="w-3 h-3" />
                    Connected
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-primary">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="text-gray-900 font-medium">@creator_main</p>
                        <p className="text-gray-600 text-sm">12,847 followers</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Shuffle className="w-3 h-3 text-primary" />
                      <span className="text-primary">Ready to share</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-center">
                    <div className="w-8 h-0.5 bg-primary"></div>
                    <div className="mx-2 p-1 bg-primary rounded-full">
                      <ArrowRight className="w-3 h-3 text-white" />
                    </div>
                    <div className="w-8 h-0.5 bg-primary"></div>
                  </div>
                  
                  <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-primary/60">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-8 h-8 bg-primary/80 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="text-gray-900 font-medium">@creator_alt</p>
                        <p className="text-gray-600 text-sm">3,241 followers</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <TrendingUp className="w-3 h-3 text-green-500" />
                      <span className="text-green-500">+2,847 new followers</span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Transfer rate</span>
                    <span className="text-green-500 font-mono">94.3%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Left Content - Swapped to right */}
          <div className="order-1 lg:order-2" data-aos="fade-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-sm font-medium text-primary mb-6">
              <Network className="w-4 h-4" />
              Cross-Promotion
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
              Automate <span className="gradient-text">Cross-Promotion</span>
              <br />
              Across Your Accounts
            </h2>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Cross-follow fans between your accounts, send strategic shoutouts, and build a connected
              network that maximizes exposure across all your profiles.
            </p>

            <div className="space-y-4 mb-8">
              <div className="flex items-start gap-3">
                <div className="text-2xl mt-1">
                  🔄
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Cross-Account Following</h3>
                  <p className="text-gray-600">Follow fans from Account A while logged into Account B to create cross-pollination</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="text-2xl mt-1">
                  🚀
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Accelerated Agency Scaling</h3>
                  <p className="text-gray-600">Scale your agency horizontally by launching new creators with instant traffic boosts from established accounts</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="text-2xl mt-1">
                  🎯
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Multi-Account Growth Strategy</h3>
                  <p className="text-gray-600">Build a connected ecosystem where each account helps grow the others organically</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 