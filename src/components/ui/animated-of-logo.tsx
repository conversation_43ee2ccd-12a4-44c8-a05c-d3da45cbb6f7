"use client";

import { motion } from 'framer-motion';

export function AnimatedOFLogo() {
  return (
    <div className="relative flex items-center justify-center w-24 h-24">
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-primary/30 rounded-full blur-2xl" />
      
      {/* Main logo container */}
      <motion.div
        className="relative flex items-center justify-center"
        animate={{
          y: [-4, 4, -4],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        {/* Exact OnlyFans SVG Logo */}
        <svg
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 400 400"
          width="80"
          height="80"
          className="relative z-10"
        >
          {/* Background rect (transparent) */}
          <rect width="400" height="400" fill="none"/>
          
          {/* Main circular path with hole */}
          <path
            d="M137.5,75a125,125,0,1,0,125,125A125,125,0,0,0,137.5,75Zm0,162.5A37.5,37.5,0,1,1,175,200,37.45,37.45,0,0,1,137.5,237.5Z"
            fill="#00aeef"
          />
          
          {/* Wing/extension path */}
          <path
            d="M278,168.75c31.76,9.14,69.25,0,69.25,0-10.88,47.5-45.38,77.25-95.13,80.87A124.73,124.73,0,0,1,137.5,325L175,205.81C213.55,83.3,233.31,75,324.73,75H387.5C377,121.25,340.81,156.58,278,168.75Z"
            fill="#008ccf"
          />
          

                  </svg>
        

      </motion.div>
      

    </div>
  );
} 