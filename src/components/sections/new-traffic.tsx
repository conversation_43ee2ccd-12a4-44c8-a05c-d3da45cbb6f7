"use client";

import { <PERSON>, Spark<PERSON>, Target } from 'lucide-react';

export function NewTraffic() {
  return (
    <section className="py-24">

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div data-aos="fade-up">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-sm font-medium text-primary mb-6">
              <Users className="w-4 h-4" />
              Fresh Traffic
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 tracking-tight">
              Reach <span className="gradient-text">Brand New Fans</span>
              <br />
              Before Your Competition
            </h2>
            
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              ofautofollower.com is the only tool that gives you access to fresh OnlyFans users.
              Our exclusive database lets you connect with new signups
              and start conversations before anyone else.
            </p>

            <div className="space-y-4 mb-8">
              <div className="flex items-start gap-3">
                <div className="text-2xl mt-1">
                  ✨
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">First-Mover Advantage</h3>
                  <p className="text-gray-600">Be the first creator new users see and interact with on the platform</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="text-2xl mt-1">
                  ✨
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Exclusive Database Access</h3>
                  <p className="text-gray-600">Tap into our proprietary network of new OnlyFans users from verified traffic sources</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Visual */}
          <div className="relative" data-aos="fade-up" data-aos-delay="200">
            <div className="bg-white p-8 rounded-2xl border border-gray-200 shadow-lg">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">New User Pipeline</h3>
                  <div className="flex items-center gap-2 text-sm text-green-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    Live
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-900 font-medium">247 new signups today</p>
                      <p className="text-gray-600 text-sm">Ready to follow</p>
                    </div>
                    <div className="text-primary font-mono text-sm">+12%</div>
                  </div>

                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <Target className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-900 font-medium">89% response rate</p>
                      <p className="text-gray-600 text-sm">From fresh users</p>
                    </div>
                    <div className="text-green-500 font-mono text-sm">+34%</div>
                  </div>
                  
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-900 font-medium">First to connect</p>
                      <p className="text-gray-600 text-sm">Zero competition</p>
                    </div>
                    <div className="text-primary font-mono text-sm">100%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 